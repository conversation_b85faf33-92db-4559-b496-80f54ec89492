# 添加泵组方案功能测试指南

## 问题修复

### 原问题
添加方案后没有反应，原因是：
1. `handleAddScheme` 方法只打印日志，没有调用后端API
2. 没有刷新方案列表
3. 缺少表单验证

### 修复内容
1. ✅ **添加后端API调用**: 调用 `savePumpGroupScheme` 接口
2. ✅ **添加数据刷新**: 保存成功后自动刷新方案列表
3. ✅ **添加表单验证**: 验证必填字段和泵组配置
4. ✅ **修复SaveRequest**: 简化后端请求类实现

## 测试步骤

### 1. 准备工作
- 确保后端服务已启动
- 确保数据库表 `sp_pump_group_scheme` 已创建
- 确保有可用的泵房数据

### 2. 功能测试

#### 2.1 基础流程测试
1. **选择泵房**: 在下拉框中选择一个泵房
2. **点击添加**: 点击"添加"按钮
3. **验证对话框**: 确认对话框正常打开
4. **查看泵机选项**: 确认下拉框显示该泵房的泵机列表

#### 2.2 表单验证测试
1. **空方案名称**: 不填写方案名称，点击提交 → 应显示"请输入方案名称"
2. **空泵组配置**: 不选择任何泵机，点击提交 → 应显示"请至少配置一个泵组"
3. **部分配置**: 只选择泵机但不填功率流量 → 应显示"请至少配置一个泵组"

#### 2.3 正常添加测试
1. **填写方案名称**: 输入"测试方案001"
2. **选择泵机**: 在第一行选择一个泵机
3. **填写参数**: 输入额定功率"15kW"，额定流量"50m³/h"
4. **填写描述**: 输入方案描述和备注
5. **提交方案**: 点击提交按钮

#### 2.4 验证结果
- ✅ 显示"方案添加成功"消息
- ✅ 对话框自动关闭
- ✅ 方案列表自动刷新，显示新添加的方案
- ✅ 新方案状态为"启用"

## 调试信息

### 前端调试
打开浏览器开发者工具，查看控制台输出：

```javascript
// 应该看到以下日志
"添加方案:" {schemeName: "测试方案001", ...}
"保存数据:" {pumpRoomId: "xxx", schemeName: "测试方案001", ...}
"保存结果:" {code: 200, data: {...}}
"解析后的泵组方案数据:" [...]
```

### 后端调试
检查后端日志，确认：
1. 接收到POST请求 `/api/sp/pumpGroupScheme`
2. 数据验证通过
3. 数据库插入成功
4. 返回成功响应

### 网络请求
在浏览器Network面板中检查：

#### 保存请求
```
POST /api/sp/pumpGroupScheme
Content-Type: application/json

{
  "pumpRoomId": "f8692bc87927871a717cd0e46d2e8b16",
  "schemeName": "测试方案001",
  "schemeCode": "SCHEME_1750844332508_123",
  "schemeDescription": "测试描述",
  "schemeRemark": "测试备注",
  "pumpGroupConfig": "[{\"pumpName\":\"pump_id_123\",\"fixedPower\":\"15kW\",\"fixedFlow\":\"50m³/h\"}]",
  "status": 1
}
```

#### 刷新请求
```
GET /api/sp/pumpGroupScheme/byPumpRoom/f8692bc87927871a717cd0e46d2e8b16
```

## 常见问题排查

### 1. 泵机下拉框为空
- 检查是否选择了泵房
- 检查 `pumpManageList` 接口是否正常返回数据
- 检查控制台是否有错误信息

### 2. 提交后没有反应
- 检查控制台是否有错误信息
- 检查网络请求是否发送成功
- 检查后端接口是否正常响应

### 3. 保存失败
- 检查必填字段是否都已填写
- 检查后端数据库连接是否正常
- 检查后端日志中的错误信息

### 4. 列表不刷新
- 检查 `getPumpStationData` 方法是否被调用
- 检查刷新接口是否返回最新数据
- 检查数据解析逻辑是否正确

## 数据格式示例

### 前端提交数据
```json
{
  "schemeName": "测试方案001",
  "schemeDescription": "这是一个测试方案",
  "schemeRemark": "用于测试功能",
  "pumpConfigs": [
    {
      "pumpName": "pump_id_123",
      "fixedPower": "15kW",
      "fixedFlow": "50m³/h",
      "pumpDisplayName": "泵组1 (P001)"
    }
  ]
}
```

### 后端保存数据
```json
{
  "pumpRoomId": "f8692bc87927871a717cd0e46d2e8b16",
  "schemeName": "测试方案001",
  "schemeCode": "SCHEME_1750844332508_123",
  "schemeDescription": "这是一个测试方案",
  "schemeRemark": "用于测试功能",
  "pumpGroupConfig": "[{\"pumpName\":\"pump_id_123\",\"fixedPower\":\"15kW\",\"fixedFlow\":\"50m³/h\"}]",
  "status": 1
}
```

现在添加方案功能应该可以正常工作了！请按照测试步骤验证功能是否正常。
