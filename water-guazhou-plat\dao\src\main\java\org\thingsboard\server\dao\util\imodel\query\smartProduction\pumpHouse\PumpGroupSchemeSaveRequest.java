package org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpGroupScheme;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.validation.NotNullOrEmpty;

@Getter
@Setter
public class PumpGroupSchemeSaveRequest extends SaveRequest<PumpGroupScheme> {
    
    // 所属泵房ID
    @NotNullOrEmpty
    private String pumpRoomId;

    // 方案名称
    @NotNullOrEmpty
    private String schemeName;

    // 方案编码
    @NotNullOrEmpty
    private String schemeCode;

    // 方案描述
    private String schemeDescription;

    // 方案备注
    private String schemeRemark;

    // 泵组配置JSON
    @NotNullOrEmpty
    private String pumpGroupConfig;

    // 是否启用 (0-禁用, 1-启用)
    private Integer status;
}
