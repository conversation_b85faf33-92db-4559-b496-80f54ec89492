package org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpGroupScheme;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class PumpGroupSchemeSaveRequest extends SaveRequest<PumpGroupScheme> {
    
    // 所属泵房ID
    @NotNullOrEmpty
    private String pumpRoomId;

    // 方案名称
    @NotNullOrEmpty
    private String schemeName;

    // 方案编码
    @NotNullOrEmpty
    private String schemeCode;

    // 方案描述
    private String schemeDescription;

    // 方案备注
    private String schemeRemark;

    // 泵组配置JSON
    @NotNullOrEmpty
    private String pumpGroupConfig;

    // 是否启用 (0-禁用, 1-启用)
    private Integer status;

    @Override
    public PumpGroupScheme build() {
        // 实现具体的构建逻辑
        PumpGroupScheme scheme = new PumpGroupScheme();
        scheme.setId(this.getId());
        scheme.setTenantId(this.getTenantIdField());
        scheme.setPumpRoomId(this.pumpRoomId);
        scheme.setSchemeName(this.schemeName);
        scheme.setSchemeCode(this.schemeCode);
        scheme.setSchemeDescription(this.schemeDescription);
        scheme.setSchemeRemark(this.schemeRemark);
        scheme.setPumpGroupConfig(this.pumpGroupConfig);
        scheme.setStatus(this.status);
        return scheme;
    }

    // 使用继承来的getter方法
    public String getTenantIdField() {
        return super.tenantId();
    }

    @Override
    protected PumpGroupScheme update(String id) {
        // 实现具体的更新逻辑
        this.setId(id);
        return build();
    }
}
