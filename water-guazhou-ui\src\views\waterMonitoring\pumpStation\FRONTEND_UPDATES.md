# 泵站调度分析页面前端更新说明

## 概述
根据需求，将页面从查询泵机数据改为查询泵组方案数据，并更新了相应的展示内容和交互功能。

## 主要修改

### 1. API接口更新
```typescript
// 原来的导入
import { pumpManageList } from '@/api/secondSupplyManage/pumpRoomInfo'

// 更新后的导入
import { getPumpGroupSchemeByPumpRoom } from '@/api/waterMonitoring/pumpGroupScheme'
```

### 2. 数据获取逻辑修改
```typescript
// 原来：获取泵机数据
const res = await pumpManageList({
  page: 1,
  size: 1000,
  pumpRoomId: queryForm.stationId
})

// 更新后：获取泵组方案数据
const res = await getPumpGroupSchemeByPumpRoom(queryForm.stationId)
```

### 3. 数据结构映射更新
```typescript
// 原来的数据映射
pumpStationData.value = pumps.map((item: any) => ({
  id: item.id,
  pumpCode: item.code,        // 设备编码
  pumpName: item.name,        // 设备名称
  pumpHouse: item.pumpRoomName,
  pumpStatus: '运行'
}))

// 更新后的数据映射
pumpStationData.value = schemes.map((item: any) => ({
  id: item.id,
  pumpCode: item.schemeCode,           // 方案编码
  pumpName: item.schemeName,           // 方案名称
  pumpHouse: item.pumpRoomName,        // 所属泵房名称
  pumpStatus: item.status === 1 ? '启用' : '禁用',
  schemeDescription: item.schemeDescription,  // 方案描述
  pumpGroupConfig: item.pumpGroupConfig,      // 泵组配置JSON
  createTime: item.createTime                 // 创建时间
}))
```

### 4. 表格展示更新
```vue
<!-- 原来的表格列 -->
<el-table-column prop="pumpCode" label="泵机编号" />
<el-table-column prop="pumpName" label="泵机名称" />
<el-table-column prop="pumpStatus" label="泵机状态" />

<!-- 更新后的表格列 -->
<el-table-column prop="pumpCode" label="方案编码" />
<el-table-column prop="pumpName" label="方案名称" />
<el-table-column prop="schemeDescription" label="方案描述" show-overflow-tooltip />
<el-table-column prop="pumpStatus" label="方案状态" />
```

### 5. 新增功能

#### 5.1 查看方案详情
```typescript
const handleView = (row: any) => {
  // 解析泵组配置JSON
  let pumpGroupConfig = []
  try {
    pumpGroupConfig = JSON.parse(row.pumpGroupConfig || '[]')
  } catch (error) {
    pumpGroupConfig = []
  }

  // 构建详情信息并显示
  const configText = pumpGroupConfig.map((pump: any) => 
    `${pump.pumpName}: ${pump.fixedPower}, ${pump.fixedFlow}`
  ).join('\n')

  ElMessageBox.alert(详情内容, '方案详情')
}
```

#### 5.2 基于实际配置的图表数据
```typescript
const generateChartData = () => {
  const categories: string[] = []
  const flowData: number[] = []
  const powerData: number[] = []

  // 遍历当前泵组方案数据，解析JSON配置
  pumpStationData.value.forEach((scheme: any) => {
    const pumpGroupConfig = JSON.parse(scheme.pumpGroupConfig || '[]')
    pumpGroupConfig.forEach((pump: any) => {
      categories.push(pump.pumpName)
      
      // 提取流量和功率数据
      const flowMatch = pump.fixedFlow?.match(/(\d+(?:\.\d+)?)/)
      const powerMatch = pump.fixedPower?.match(/(\d+(?:\.\d+)?)/)
      
      flowData.push(flowMatch ? parseFloat(flowMatch[1]) : 50)
      powerData.push(powerMatch ? parseFloat(powerMatch[1]) : 15)
    })
  })

  return { categories, flowData, powerData }
}
```

### 6. 图表更新

#### 6.1 供水量对比图表
- **数据来源**: 基于泵组方案的实际配置
- **显示内容**: 设计流量 vs 实际流量
- **单位**: m³/h

#### 6.2 功率消耗图表
- **数据来源**: 基于泵组方案的功率配置
- **显示内容**: 各泵组的设备功率
- **单位**: kW

### 7. 错误处理和容错
```typescript
// 接口调用失败时的模拟数据
pumpStationData.value = [
  {
    id: '1',
    pumpCode: 'LCHX_STD_001',
    pumpName: '标准运行方案',
    pumpHouse: '龙车红星泵站',
    pumpStatus: '启用',
    schemeDescription: '标准运行方案，适用于正常供水需求',
    pumpGroupConfig: '[{"pumpName":"泵组1","fixedPower":"15kW","fixedFlow":"50m³/h"}]'
  }
]
```

## 数据流程

1. **页面初始化**: 调用泵房台账接口获取泵房列表
2. **选择泵房**: 用户在下拉中选择具体泵房
3. **查询方案**: 调用 `getPumpGroupSchemeByPumpRoom(pumpRoomId)` 获取该泵房的泵组方案
4. **数据展示**: 在表格中显示方案编码、名称、描述、状态
5. **图表更新**: 解析泵组配置JSON，生成基于实际配置的图表数据
6. **交互功能**: 支持查看详情、编辑、删除操作

## 泵组配置JSON格式
```json
[
  {
    "pumpName": "泵组1",
    "fixedPower": "15kW",
    "fixedFlow": "50m³/h"
  },
  {
    "pumpName": "泵组2", 
    "fixedPower": "18kW",
    "fixedFlow": "60m³/h"
  }
]
```

## 测试验证

1. **选择泵房**: 验证下拉选择是否正确显示泵房列表
2. **方案查询**: 验证选择泵房后是否正确显示对应的泵组方案
3. **详情查看**: 验证点击"查看"按钮是否正确解析并显示泵组配置
4. **图表更新**: 验证图表是否基于实际的泵组配置数据生成
5. **容错处理**: 验证接口失败时是否正确显示模拟数据

现在页面完全基于泵组方案数据，展示内容更加符合业务需求！
