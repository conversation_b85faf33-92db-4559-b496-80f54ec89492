package org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("sp_pump_group_scheme")
// 泵组方案
public class PumpGroupScheme {
    // id
    @TableId
    private String id;

    // 所属泵房ID
    private String pumpRoomId;

    // 所属泵房编码
    private String pumpRoomCode;

    // 所属泵房名称
    private String pumpRoomName;

    // 方案名称
    private String schemeName;

    // 方案编码
    private String schemeCode;

    // 方案描述
    private String schemeDescription;

    // 方案备注
    private String schemeRemark;

    // 泵组配置JSON (存储泵组配置的详细信息)
    private String pumpGroupConfig;

    // 是否启用 (0-禁用, 1-启用)
    private Integer status;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 更新人
    @ParseUsername
    private String updater;

    // 更新时间
    private Date updateTime;

    // 租户ID
    private String tenantId;

}
