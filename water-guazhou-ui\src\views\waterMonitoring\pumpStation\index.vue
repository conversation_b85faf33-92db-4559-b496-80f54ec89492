<template>
  <div class="pump-station-dispatch">
    <!-- 筛选面板 -->
    <el-card class="filter-card">
      <div class="filter-panel">
        <el-form :model="queryForm" inline>
          <el-form-item label="数据来源:">
            <el-select v-model="queryForm.stationId" placeholder="请选择" style="width: 200px;" @change="handleStationChange">
              <el-option
                v-for="item in stationOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="方案名称:">
            <el-input v-model="queryForm.schemeName" placeholder="输入方案" style="width: 200px;" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 主要内容区域 -->
    <div class="content-container">
      <el-row :gutter="16">
        <!-- 左侧泵房列表 -->
        <el-col :span="14">
          <el-card class="pump-list-card">
            <template #header>
              <div class="card-header">
                <span>泵站运行</span>
                <el-button type="primary" size="small" @click="showAddSchemeDialog">添加</el-button>
              </div>
            </template>

            <el-table :data="pumpStationData" border stripe height="400">
              <el-table-column type="selection" width="55" />
              <el-table-column prop="pumpCode" label="泵机编号" min-width="120" />
              <el-table-column prop="pumpName" label="泵机名称" min-width="140" />
              <el-table-column prop="pumpHouse" label="所属泵房" min-width="120" />
              <el-table-column prop="pumpStatus" label="泵机状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.pumpStatus === '运行' ? 'success' : 'info'">
                    {{ row.pumpStatus }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="140">
                <template #default="{ row }">
                  <el-button type="primary" link size="small" @click="handleEdit(row)">编辑</el-button>
                  <el-button type="primary" link size="small" @click="handleDelete(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>

        <!-- 右侧图表区域 -->
        <el-col :span="10">
          <!-- 上方供水量对比图 -->
          <el-card class="chart-card" style="margin-bottom: 16px;">
            <template #header>
              <div class="chart-header">
                <span>供水量对比图表</span>
                <div class="chart-legend">
                  <span class="legend-item">
                    <i class="legend-dot" style="background-color: #5470c6;"></i>
                    中水泵
                  </span>
                  <span class="legend-item">
                    <i class="legend-dot" style="background-color: #91cc75;"></i>
                    供水泵
                  </span>
                </div>
              </div>
            </template>
            <div ref="waterSupplyChart" style="height: 180px;"></div>
          </el-card>

          <!-- 下方泵组耗水耗电图 -->
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>泵组耗水耗电</span>
                <div class="chart-legend">
                  <span class="legend-item">
                    <i class="legend-dot" style="background-color: #fac858;"></i>
                    耗水电耗
                  </span>
                  <span style="margin-left: 20px;">单位：kWh/t</span>
                </div>
              </div>
            </template>
            <div ref="powerConsumptionChart" style="height: 180px;"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 添加方案对话框 -->
    <AddSchemeDialog
      v-model:visible="addSchemeDialogVisible"
      @confirm="handleAddScheme"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import AddSchemeDialog from './components/AddSchemeDialog.vue'
import { pumpHouseStorageList, pumpManageList } from '@/api/secondSupplyManage/pumpRoomInfo'
import { ElMessage, ElMessageBox } from 'element-plus'

// 状态定义
const loading = ref(false)
const stationOptions = ref<Array<{label: string, value: string}>>([])
const addSchemeDialogVisible = ref(false)

// 查询表单
const queryForm = reactive({
  stationId: '',
  schemeName: ''
})

// 泵站数据
const pumpStationData = ref<Array<any>>([])

// 图表引用
const waterSupplyChart = ref<HTMLElement>()
const powerConsumptionChart = ref<HTMLElement>()
let waterSupplyChartInstance: echarts.ECharts | null = null
let powerConsumptionChartInstance: echarts.ECharts | null = null

// 获取泵房列表
const getStationList = async () => {
  try {
    const res = await pumpHouseStorageList({
      page: 1,
      size: 1000
    })

    console.log('泵房接口返回数据:', res)

    // 处理泵房数据
    let stations: any[] = []

    // 检查响应结构
    if (res && res.data) {
      if (res.data.code === 200) {
        if (res.data.data && Array.isArray(res.data.data.records)) {
          stations = res.data.data.records
        } else if (Array.isArray(res.data.data)) {
          stations = res.data.data
        }
      } else if (Array.isArray(res.data)) {
        stations = res.data
      }
    }

    console.log('解析后的泵房数据:', stations)

    // 如果没有获取到数据，使用模拟数据
    if (stations.length === 0) {
      console.log('未获取到泵房数据，使用模拟数据')
      stations = [
        { id: '1', name: '清河泵房', pumpHouseName: '清河泵房' },
        { id: '2', name: '瓜州泵房', pumpHouseName: '瓜州泵房' },
        { id: '3', name: '锁阳城泵房', pumpHouseName: '锁阳城泵房' }
      ]
    }

    // 转换为下拉选项格式
    stationOptions.value = stations.map((item: any) => ({
      label: item.name || item.pumpHouseName,
      value: item.id
    }))

    console.log('泵房列表获取成功:', stationOptions.value)
  } catch (error) {
    console.error('获取泵房列表失败:', error)
    ElMessage.error('获取泵房列表失败')

    // 接口失败时使用模拟数据
    stationOptions.value = [
      { label: '清河泵房', value: '1' },
      { label: '瓜州泵房', value: '2' },
      { label: '锁阳城泵房', value: '3' }
    ]
  }
}

// 获取泵站数据
const getPumpStationData = async () => {
  if (!queryForm.stationId) {
    pumpStationData.value = []
    return
  }

  loading.value = true
  try {
    // 根据选中的泵房ID获取泵机数据
    const res = await pumpManageList({
      page: 1,
      size: 1000,
      pumpRoomId: queryForm.stationId // 根据泵房ID查询泵机，使用正确的字段名
    })

    console.log('泵机接口返回数据:', res)
    console.log('查询泵房ID:', queryForm.stationId)

    let pumps: any[] = []

    // 处理响应数据
    if (res && res.data) {
      if (res.data.code === 200) {
        if (res.data.data && Array.isArray(res.data.data.records)) {
          pumps = res.data.data.records
        } else if (Array.isArray(res.data.data)) {
          pumps = res.data.data
        }
      } else if (Array.isArray(res.data)) {
        pumps = res.data
      }
    }

    console.log('解析后的泵机数据:', pumps)
    console.log('泵机数据数量:', pumps.length)

    // 转换数据格式，使用正确的字段名
    pumpStationData.value = pumps.map((item: any) => ({
      id: item.id,
      pumpCode: item.code, // 设备编码
      pumpName: item.name, // 设备名称
      pumpHouse: item.pumpRoomName || '泵房', // 所属泵房名称
      pumpStatus: item.status === '1' || item.status === 'running' ? '运行' : '停机'
    }))

    // 如果没有数据，显示提示
    if (pumpStationData.value.length === 0) {
      ElMessage.info('该泵房暂无泵机数据')
    }

    // 更新图表
    await nextTick()
    initCharts()
  } catch (error) {
    console.error('获取泵站数据失败:', error)
    ElMessage.error('获取泵站数据失败')

    // 如果接口失败，使用模拟数据
    pumpStationData.value = [
      {
        id: '1',
        pumpCode: 'A1#增压泵房',
        pumpName: '增压泵房',
        pumpHouse: '内房',
        pumpStatus: '运行'
      },
      {
        id: '2',
        pumpCode: '变频工厂水泵',
        pumpName: '水泵站房',
        pumpHouse: '内房',
        pumpStatus: '停机'
      }
    ]

    await nextTick()
    initCharts()
  } finally {
    loading.value = false
  }
}

// 站点变化处理
const handleStationChange = () => {
  getPumpStationData()
}

// 查询处理
const handleQuery = () => {
  getPumpStationData()
}

// 重置处理
const handleReset = () => {
  queryForm.stationId = ''
  queryForm.schemeName = ''
  pumpStationData.value = []
  destroyCharts()
}

// 编辑处理
const handleEdit = (row: any) => {
  console.log('编辑:', row)
  ElMessage.info('编辑功能待实现')
}

// 删除处理
const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 删除逻辑
    const index = pumpStationData.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      pumpStationData.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 显示添加方案对话框
const showAddSchemeDialog = () => {
  addSchemeDialogVisible.value = true
}

// 添加方案处理
const handleAddScheme = (schemeData: any) => {
  console.log('添加方案:', schemeData)
  ElMessage.success('方案添加成功')
  addSchemeDialogVisible.value = false
}

// 初始化图表
const initCharts = () => {
  initWaterSupplyChart()
  initPowerConsumptionChart()
}

// 初始化供水量对比图表
const initWaterSupplyChart = () => {
  if (!waterSupplyChart.value) return

  if (waterSupplyChartInstance) {
    waterSupplyChartInstance.dispose()
  }

  waterSupplyChartInstance = echarts.init(waterSupplyChart.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      show: false
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['泵组1', '泵组2', '泵组3', '泵组4', '泵组5']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '中水泵',
        type: 'bar',
        data: [250, 280, 230, 260, 300],
        itemStyle: {
          color: '#5470c6'
        }
      },
      {
        name: '供水泵',
        type: 'bar',
        data: [180, 200, 170, 190, 220],
        itemStyle: {
          color: '#91cc75'
        }
      }
    ]
  }

  waterSupplyChartInstance.setOption(option)
}

// 初始化泵组耗水耗电图表
const initPowerConsumptionChart = () => {
  if (!powerConsumptionChart.value) return

  if (powerConsumptionChartInstance) {
    powerConsumptionChartInstance.dispose()
  }

  powerConsumptionChartInstance = echarts.init(powerConsumptionChart.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      show: false
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['泵组1', '泵组2', '泵组3', '泵组4', '泵组5']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '耗水电耗',
        type: 'bar',
        data: [250, 250, 500, 500, 450],
        itemStyle: {
          color: '#fac858'
        }
      }
    ]
  }

  powerConsumptionChartInstance.setOption(option)
}

// 销毁图表
const destroyCharts = () => {
  if (waterSupplyChartInstance) {
    waterSupplyChartInstance.dispose()
    waterSupplyChartInstance = null
  }
  if (powerConsumptionChartInstance) {
    powerConsumptionChartInstance.dispose()
    powerConsumptionChartInstance = null
  }
}

onMounted(() => {
  getStationList()
})
</script>

<style scoped lang="scss">
.pump-station-dispatch {
  padding: 16px;

  .filter-card {
    margin-bottom: 16px;

    .filter-panel {
      .el-form {
        display: flex;
        flex-wrap: wrap;

        .el-form-item {
          margin-right: 20px;
          margin-bottom: 10px;
        }
      }
    }
  }

  .content-container {
    .pump-list-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .chart-card {
      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .chart-legend {
          display: flex;
          align-items: center;

          .legend-item {
            display: flex;
            align-items: center;
            margin-right: 20px;
            font-size: 12px;

            .legend-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              margin-right: 4px;
            }
          }
        }
      }
    }
  }
}
</style>
