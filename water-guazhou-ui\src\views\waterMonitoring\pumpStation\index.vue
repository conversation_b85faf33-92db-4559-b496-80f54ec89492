<template>
  <div class="pump-station-dispatch">
    <!-- 筛选面板 -->
    <el-card class="filter-card">
      <div class="filter-panel">
        <el-form :model="queryForm" inline>
          <el-form-item label="选择泵房:">
            <el-select v-model="queryForm.stationId" placeholder="请选择" style="width: 200px;" @change="handleStationChange">
              <el-option
                v-for="item in stationOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="方案名称:">
            <el-input v-model="queryForm.schemeName" placeholder="输入方案" style="width: 200px;" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 主要内容区域 -->
    <div class="content-container">
      <el-row :gutter="16">
        <!-- 左侧泵房列表 -->
        <el-col :span="14">
          <el-card class="pump-list-card">
            <template #header>
              <div class="card-header">
                <span>泵组方案</span>
                <el-button type="primary" size="small" @click="showAddSchemeDialog">添加</el-button>
              </div>
            </template>

            <el-table :data="pumpStationData" border stripe height="400">
              <el-table-column type="selection" width="55" />
              <el-table-column prop="pumpCode" label="方案编码" min-width="120" />
              <el-table-column prop="pumpName" label="方案名称" min-width="140" />
              <el-table-column prop="pumpHouse" label="所属泵房" min-width="120" />
              <el-table-column prop="schemeDescription" label="方案描述" min-width="180" show-overflow-tooltip />
              <el-table-column prop="pumpStatus" label="方案状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.pumpStatus === '启用' ? 'success' : 'info'">
                    {{ row.pumpStatus }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="180">
                <template #default="{ row }">
                  <el-button type="primary" link size="small" @click="handleView(row)">查看</el-button>
                  <el-button type="primary" link size="small" @click="handleEdit(row)">编辑</el-button>
                  <el-button type="danger" link size="small" @click="handleDelete(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>

        <!-- 右侧图表区域 -->
        <el-col :span="10">
          <!-- 上方供水量对比图 -->
          <el-card class="chart-card" style="margin-bottom: 16px;">
            <template #header>
              <div class="chart-header">
                <span>供水量对比图表</span>
                <div class="chart-legend">
                  <span class="legend-item">
                    <i class="legend-dot" style="background-color: #5470c6;"></i>
                    中水泵
                  </span>
                  <span class="legend-item">
                    <i class="legend-dot" style="background-color: #91cc75;"></i>
                    供水泵
                  </span>
                </div>
              </div>
            </template>
            <div ref="waterSupplyChart" style="height: 180px;"></div>
          </el-card>

          <!-- 下方泵组耗水耗电图 -->
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>泵组耗水耗电</span>
                <div class="chart-legend">
                  <span class="legend-item">
                    <i class="legend-dot" style="background-color: #fac858;"></i>
                    耗水电耗
                  </span>
                  <span style="margin-left: 20px;">单位：kWh/t</span>
                </div>
              </div>
            </template>
            <div ref="powerConsumptionChart" style="height: 180px;"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 添加方案对话框 -->
    <AddSchemeDialog
      v-model:visible="addSchemeDialogVisible"
      @confirm="handleAddScheme"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import AddSchemeDialog from './components/AddSchemeDialog.vue'
import { pumpHouseStorageList } from '@/api/secondSupplyManage/pumpRoomInfo'
import { getPumpGroupSchemeByPumpRoom } from '@/api/waterMonitoring/pumpGroupScheme'
import { ElMessage, ElMessageBox } from 'element-plus'

// 状态定义
const loading = ref(false)
const stationOptions = ref<Array<{label: string, value: string}>>([])
const addSchemeDialogVisible = ref(false)

// 查询表单
const queryForm = reactive({
  stationId: '',
  schemeName: ''
})

// 泵站数据
const pumpStationData = ref<Array<any>>([])

// 图表引用
const waterSupplyChart = ref<HTMLElement>()
const powerConsumptionChart = ref<HTMLElement>()
let waterSupplyChartInstance: echarts.ECharts | null = null
let powerConsumptionChartInstance: echarts.ECharts | null = null

// 获取泵房列表
const getStationList = async () => {
  try {
    const res = await pumpHouseStorageList({
      page: 1,
      size: 1000
    })
    // 处理泵房数据
    let stations: any[] = []

    // 根据实际返回的数据结构解析
    if (res && res.data && res.data.code === 200) {
      if (res.data.data && Array.isArray(res.data.data.data)) {
        stations = res.data.data.data
      }
    }


    // 转换为下拉选项格式，使用实际的字段名
    stationOptions.value = stations.map((item: any) => ({
      label: item.name, // 泵房名称
      value: item.id    // 泵房ID
    }))
  } catch (error) {
    console.error('获取泵房列表失败:', error)
    ElMessage.error('获取泵房列表失败')
  }
}

// 获取泵组方案数据
const getPumpStationData = async () => {
  if (!queryForm.stationId) {
    pumpStationData.value = []
    return
  }

  loading.value = true
  try {
    // 根据选中的泵房ID获取泵组方案数据
    const res = await getPumpGroupSchemeByPumpRoom(queryForm.stationId)

    console.log('泵组方案接口返回数据:', res)
    console.log('查询泵房ID:', queryForm.stationId)

    let schemes: any[] = []

    // 根据后端接口返回的数据结构解析
    if (res && res.data) {
      if (res.data.code === 200) {
        // 直接返回数组的情况
        if (Array.isArray(res.data.data)) {
          schemes = res.data.data
        }
      } else if (Array.isArray(res.data)) {
        // 直接返回数组的情况
        schemes = res.data
      }
    }

    console.log('解析后的泵组方案数据:', schemes)
    console.log('泵组方案数据数量:', schemes.length)

    // 转换数据格式，显示泵组方案信息
    pumpStationData.value = schemes.map((item: any) => ({
      id: item.id,
      pumpCode: item.schemeCode, // 方案编码
      pumpName: item.schemeName, // 方案名称
      pumpHouse: item.pumpRoomName || '泵房', // 所属泵房名称
      pumpStatus: item.status === 1 ? '启用' : '禁用', // 方案状态
      schemeDescription: item.schemeDescription, // 方案描述
      pumpGroupConfig: item.pumpGroupConfig, // 泵组配置
      createTime: item.createTime // 创建时间
    }))

    // 如果没有数据，显示提示
    if (pumpStationData.value.length === 0) {
      ElMessage.info('该泵房暂无泵组方案数据')
    }

    // 更新图表
    await nextTick()
    initCharts()
  } catch (error) {
    console.error('获取泵组方案数据失败:', error)
    ElMessage.error('获取泵组方案数据失败')

    // 如果接口失败，使用模拟数据
    pumpStationData.value = [
      {
        id: '1',
        pumpCode: 'LCHX_STD_001',
        pumpName: '标准运行方案',
        pumpHouse: '龙车红星泵站',
        pumpStatus: '启用',
        schemeDescription: '标准运行方案，适用于正常供水需求',
        pumpGroupConfig: '[{"pumpName":"泵组1","fixedPower":"15kW","fixedFlow":"50m³/h"}]'
      },
      {
        id: '2',
        pumpCode: 'LCHX_PEAK_001',
        pumpName: '高峰运行方案',
        pumpHouse: '龙车红星泵站',
        pumpStatus: '启用',
        schemeDescription: '高峰期运行方案，适用于用水高峰时段',
        pumpGroupConfig: '[{"pumpName":"泵组1","fixedPower":"15kW","fixedFlow":"50m³/h"},{"pumpName":"泵组2","fixedPower":"18kW","fixedFlow":"60m³/h"}]'
      }
    ]

    await nextTick()
    initCharts()
  } finally {
    loading.value = false
  }
}

// 站点变化处理
const handleStationChange = () => {
  getPumpStationData()
}

// 查询处理
const handleQuery = () => {
  getPumpStationData()
}

// 重置处理
const handleReset = () => {
  queryForm.stationId = ''
  queryForm.schemeName = ''
  pumpStationData.value = []
  destroyCharts()
}

// 查看方案详情
const handleView = (row: any) => {
  console.log('查看方案详情:', row)

  // 解析泵组配置JSON
  let pumpGroupConfig = []
  try {
    pumpGroupConfig = JSON.parse(row.pumpGroupConfig || '[]')
  } catch (error) {
    console.error('解析泵组配置失败:', error)
    pumpGroupConfig = []
  }

  // 构建详情信息
  const configText = pumpGroupConfig.map((pump: any) =>
    `${pump.pumpName}: ${pump.fixedPower}, ${pump.fixedFlow}`
  ).join('\n')

  ElMessageBox.alert(
    `方案编码: ${row.pumpCode}\n方案名称: ${row.pumpName}\n所属泵房: ${row.pumpHouse}\n方案描述: ${row.schemeDescription || '无'}\n泵组配置:\n${configText || '无配置信息'}`,
    '方案详情',
    {
      confirmButtonText: '确定',
      type: 'info'
    }
  )
}

// 编辑处理
const handleEdit = (row: any) => {
  console.log('编辑:', row)
  ElMessage.info('编辑功能待实现')
}

// 删除处理
const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确定要删除方案"${row.pumpName}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 删除逻辑
    const index = pumpStationData.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      pumpStationData.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 显示添加方案对话框
const showAddSchemeDialog = () => {
  addSchemeDialogVisible.value = true
}

// 添加方案处理
const handleAddScheme = (schemeData: any) => {
  console.log('添加方案:', schemeData)
  ElMessage.success('方案添加成功')
  addSchemeDialogVisible.value = false
}

// 生成图表数据
const generateChartData = () => {
  const categories: string[] = []
  const flowData: number[] = []
  const actualFlowData: number[] = []
  const powerData: number[] = []

  // 遍历当前泵组方案数据
  pumpStationData.value.forEach((scheme: any) => {
    try {
      const pumpGroupConfig = JSON.parse(scheme.pumpGroupConfig || '[]')
      pumpGroupConfig.forEach((pump: any, index: number) => {
        const categoryName = pump.pumpName || `泵组${index + 1}`
        categories.push(categoryName)

        // 提取流量数据 (假设格式为 "50m³/h")
        const flowMatch = pump.fixedFlow?.match(/(\d+(?:\.\d+)?)/)
        const flow = flowMatch ? parseFloat(flowMatch[1]) : 50
        flowData.push(flow)

        // 生成实际流量数据 (设计流量的80%-95%)
        actualFlowData.push(Math.round(flow * (0.8 + Math.random() * 0.15)))

        // 提取功率数据 (假设格式为 "15kW")
        const powerMatch = pump.fixedPower?.match(/(\d+(?:\.\d+)?)/)
        const power = powerMatch ? parseFloat(powerMatch[1]) : 15
        powerData.push(power)
      })
    } catch (error) {
      console.error('解析泵组配置失败:', error)
    }
  })

  // 如果没有数据，使用默认数据
  if (categories.length === 0) {
    return {
      categories: ['泵组1', '泵组2', '泵组3'],
      flowData: [50, 60, 45],
      actualFlowData: [45, 55, 40],
      powerData: [15, 18, 12]
    }
  }

  return {
    categories,
    flowData,
    actualFlowData,
    powerData
  }
}

// 初始化图表
const initCharts = () => {
  initWaterSupplyChart()
  initPowerConsumptionChart()
}

// 初始化供水量对比图表
const initWaterSupplyChart = () => {
  if (!waterSupplyChart.value) return

  if (waterSupplyChartInstance) {
    waterSupplyChartInstance.dispose()
  }

  waterSupplyChartInstance = echarts.init(waterSupplyChart.value)

  // 基于当前选中泵房的方案数据生成图表
  const chartData = generateChartData()

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      show: false
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.categories
    },
    yAxis: {
      type: 'value',
      name: '流量(m³/h)'
    },
    series: [
      {
        name: '设计流量',
        type: 'bar',
        data: chartData.flowData,
        itemStyle: {
          color: '#5470c6'
        }
      },
      {
        name: '实际流量',
        type: 'bar',
        data: chartData.actualFlowData,
        itemStyle: {
          color: '#91cc75'
        }
      }
    ]
  }

  waterSupplyChartInstance.setOption(option)
}

// 初始化泵组耗水耗电图表
const initPowerConsumptionChart = () => {
  if (!powerConsumptionChart.value) return

  if (powerConsumptionChartInstance) {
    powerConsumptionChartInstance.dispose()
  }

  powerConsumptionChartInstance = echarts.init(powerConsumptionChart.value)

  // 基于当前选中泵房的方案数据生成图表
  const chartData = generateChartData()

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        const data = params[0]
        return `${data.name}<br/>功率: ${data.value}kW`
      }
    },
    legend: {
      show: false
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.categories
    },
    yAxis: {
      type: 'value',
      name: '功率(kW)'
    },
    series: [
      {
        name: '设备功率',
        type: 'bar',
        data: chartData.powerData,
        itemStyle: {
          color: '#fac858'
        }
      }
    ]
  }

  powerConsumptionChartInstance.setOption(option)
}

// 销毁图表
const destroyCharts = () => {
  if (waterSupplyChartInstance) {
    waterSupplyChartInstance.dispose()
    waterSupplyChartInstance = null
  }
  if (powerConsumptionChartInstance) {
    powerConsumptionChartInstance.dispose()
    powerConsumptionChartInstance = null
  }
}

onMounted(() => {
  getStationList()
})
</script>

<style scoped lang="scss">
.pump-station-dispatch {
  padding: 16px;

  .filter-card {
    margin-bottom: 16px;

    .filter-panel {
      .el-form {
        display: flex;
        flex-wrap: wrap;

        .el-form-item {
          margin-right: 20px;
          margin-bottom: 10px;
        }
      }
    }
  }

  .content-container {
    .pump-list-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .chart-card {
      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .chart-legend {
          display: flex;
          align-items: center;

          .legend-item {
            display: flex;
            align-items: center;
            margin-right: 20px;
            font-size: 12px;

            .legend-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              margin-right: 4px;
            }
          }
        }
      }
    }
  }
}
</style>
