package org.thingsboard.server.dao.pumpHouse;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpGroupScheme;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpGroupSchemePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpGroupSchemeSaveRequest;

import java.util.List;

public interface PumpGroupSchemeService {
    
    /**
     * 分页条件查询泵组方案
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<PumpGroupScheme> findAllConditional(PumpGroupSchemePageRequest request);

    /**
     * 保存泵组方案
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    PumpGroupScheme save(PumpGroupSchemeSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(PumpGroupScheme entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 批量保存
     *
     * @param entities 泵组方案信息列表
     * @return 保存好的数据
     */
    List<PumpGroupScheme> saveAll(List<PumpGroupSchemeSaveRequest> entities);

    /**
     * 根据泵房ID获取方案列表
     *
     * @param pumpRoomId 泵房ID
     * @return 方案列表
     */
    List<PumpGroupScheme> findByPumpRoomId(String pumpRoomId);

    /**
     * 根据方案编码获取方案信息
     *
     * @param schemeCode 方案编码
     * @return 方案信息
     */
    PumpGroupScheme findBySchemeCode(String schemeCode);

}
