<template>
  <div class="sidebar" :class="{ collapsed: isCollapsed }">
    <div class="menu-bg overlay-y" :class="{ show: appStore.menuShow }">
      <el-menu
        ref="refMenu"
        mode="vertical"
        router
        :show-timeout="200"
        :default-active="appStore.subMenuParentRoute?.path"
        :unique-opened="true"
        :collapse="isCollapsed"
      >
        <template v-for="(route, index) in menuRoutes">
          <sidebar-item
            v-if="!route.hidden"
            :key="index"
            :item="route"
            :level="0"
            :icon="route.meta?.icon"
            :collapsed="isCollapsed"
          ></sidebar-item>
        </template>
      </el-menu>
    </div>
    <SubSideBar
      v-show="appStore.menuShow && appStore.subMenuShow"
    ></SubSideBar>
    <div class="sidebar-toggle-bottom" @click="toggleCollapse">
      <el-icon :size="20" style="cursor: pointer;color: #0080FF;">
        <ArrowRight v-if="isCollapsed"/>
        <ArrowLeft  v-else/>
      </el-icon>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useAppStore, usePermissionStore } from '@/store';
import { filterRoutes } from '@/utils/RouterHelper';
import { useRouter } from 'vue-router';
import { ref, computed } from 'vue';
import SidebarItem from './SidebarItem.vue';
import SubSideBar from './SubSideBar.vue';
import { ArrowLeft, ArrowRight,Operation } from '@element-plus/icons-vue';
const keyword = ref<string>('');
const isCollapsed = ref(false);
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};
const appStore = useAppStore();
const menuRoutes = computed(() => {
  return filterRoutes(usePermissionStore().routers, keyword.value, []);
});
const router = useRouter();
const handleSubMenuClick = async (cMenu?: any) => {
  appStore.TOGGLE_submenuShow(true);
  appStore.SET_subMenuParentRoute(cMenu);
};
</script>

<style lang="scss" scoped>
.sidebar {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 200px;
  position: relative;
  transition: width 0.2s;
  z-index: 2000;
  &.collapsed {
    width: 60px;
    .el-menu {
      width: 60px !important;
      min-width: 60px !important;
    }
  }
}
.menu-bg {
  flex: 1;
  width: 100%;
  height: 100%;
  position: relative;
  background-color: var(--el-bg-color);
  z-index: 1;
}
.sidebar-toggle-bottom {
  width: 100%;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-top: 1px solid #e4e7ed;
  background: #fff;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 10;
  transition: width 0.2s;
}
.el-menu {
  border: none;
  width: 100%;
  background-color: var(--el-bg-color);
  transition: width 0.2s, min-width 0.2s;
  .el-sub-menu .el-sub-menu__title {
    padding-right: 40px !important;
  }
  .el-menu--popup {
    min-width: 200px;
  }
  // 默认菜单项文字颜色和字号
  :deep(.el-menu-item), :deep(.el-sub-menu__title) {
    color: #8599AD !important;
    font-size: 14px !important;
  }
}
// 鼠标移入菜单项字体变色，背景不变
:deep(.el-menu-item), :deep(.el-sub-menu__title) {
  transition: color 0.2s;
}
:deep(.el-menu-item:hover), :deep(.el-sub-menu__title:hover) {
  color: #0080FF !important;
  background: none !important;
}
:deep(.el-menu-item.is-active), :deep(.el-sub-menu__title.is-active) {
  color: #0080FF !important;
  background: none !important;
}
</style>

<style lang="scss">
// 折叠模式下的弹出菜单样式
.sidebar-popper {
  .el-menu {
    background-color: var(--el-bg-color) !important;
    border: none !important;
    .el-menu-item, .el-sub-menu__title {
      color: #8599AD !important;
      font-size: 14px !important;
      &:hover {
        color: #0080FF !important;
        background: none !important;
      }
      &.is-active {
        color: #0080FF !important;
        background: none !important;
      }
    }
  }
  .el-menu--popup {
    min-width: 200px;
    margin: 0;
    padding: 5px 0;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  }
}
</style>
