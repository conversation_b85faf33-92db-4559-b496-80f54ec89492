# 泵组方案管理接口文档

## 概述
为泵站调度分析页面生成的泵组方案管理后端接口，支持根据泵房查询相关的泵组方案。

## 数据库表结构

### sp_pump_group_scheme (泵组方案表)
```sql
CREATE TABLE sp_pump_group_scheme (
    id VARCHAR(36) NOT NULL COMMENT '主键ID',
    pump_room_id VARCHAR(36) NOT NULL COMMENT '所属泵房ID',
    scheme_name VARCHAR(100) NOT NULL COMMENT '方案名称',
    scheme_code VARCHAR(50) NOT NULL COMMENT '方案编码',
    scheme_description TEXT COMMENT '方案描述',
    scheme_remark TEXT COMMENT '方案备注',
    pump_group_config TEXT NOT NULL COMMENT '泵组配置JSON',
    status TINYINT(1) DEFAULT 1 COMMENT '状态(0-禁用,1-启用)',
    creator VARCHAR(36) COMMENT '创建人',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(36) COMMENT '更新人',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    tenant_id VARCHAR(36) COMMENT '租户ID',
    PRIMARY KEY (id)
);
```

## 后端接口

### 1. 控制器 (PumpGroupSchemeController)
- **路径**: `/api/sp/pumpGroupScheme`
- **功能**: 提供泵组方案的CRUD操作

#### 主要接口:
- `GET /api/sp/pumpGroupScheme` - 分页查询泵组方案
- `GET /api/sp/pumpGroupScheme/byPumpRoom/{pumpRoomId}` - 根据泵房ID获取方案列表
- `GET /api/sp/pumpGroupScheme/bySchemeCode/{schemeCode}` - 根据方案编码获取方案
- `POST /api/sp/pumpGroupScheme` - 保存泵组方案
- `POST /api/sp/pumpGroupScheme/batch` - 批量保存泵组方案
- `PATCH /api/sp/pumpGroupScheme/{id}` - 更新泵组方案
- `DELETE /api/sp/pumpGroupScheme/{id}` - 删除泵组方案

### 2. 服务层 (PumpGroupSchemeService)
- **实现类**: PumpGroupSchemeServiceImpl
- **功能**: 业务逻辑处理，包括数据验证和关联查询

### 3. 数据访问层 (PumpGroupSchemeMapper)
- **映射文件**: PumpGroupSchemeMapper.xml
- **功能**: 数据库操作，支持复杂查询和关联查询

## 前端集成

### API文件
- **路径**: `water-guazhou-ui/src/api/waterMonitoring/pumpGroupScheme.ts`
- **功能**: 封装后端接口调用

### 页面更新
- **文件**: `water-guazhou-ui/src/views/waterMonitoring/pumpStation/index.vue`
- **更新内容**:
  - 导入泵组方案API
  - 修改数据获取逻辑，从获取泵机改为获取泵组方案
  - 更新表格列标题和数据映射

## 数据流程

1. **页面加载**: 获取泵房列表填充下拉选择
2. **选择泵房**: 调用 `getPumpGroupSchemeByPumpRoom(pumpRoomId)` 获取该泵房的方案列表
3. **数据展示**: 在表格中显示方案编码、方案名称、所属泵房、方案状态
4. **操作功能**: 支持添加、编辑、删除方案

## 示例数据

系统预置了示例数据：
- 龙车红星泵站标准方案 (LCHX_STD_001)
- 龙车红星泵站高峰方案 (LCHX_PEAK_001)  
- 龙车柑子院泵站标准方案 (LCGZY_STD_001)

## 泵组配置JSON格式

```json
[
  {
    "pumpName": "泵组1",
    "fixedPower": "15kW", 
    "fixedFlow": "50m³/h"
  },
  {
    "pumpName": "泵组2",
    "fixedPower": "18kW",
    "fixedFlow": "60m³/h"
  }
]
```

## 部署说明

1. **执行SQL脚本**: 运行 `sql/sp_pump_group_scheme.sql` 创建表和示例数据
2. **重启服务**: 重启后端服务以加载新的接口
3. **前端更新**: 确保前端代码已更新并重新构建

## 测试验证

1. 访问泵站调度分析页面
2. 选择泵房，验证是否正确显示对应的泵组方案
3. 测试添加、编辑、删除功能
4. 验证数据持久化和状态更新
