# 泵组方案管理接口文档

## 概述
为泵站调度分析页面生成的泵组方案管理后端接口，支持根据泵房查询相关的泵组方案。

## 数据库表结构

### sp_pump_group_scheme (泵组方案表) - PostgreSQL
```sql
CREATE TABLE IF NOT EXISTS sp_pump_group_scheme (
    id varchar(31) NOT NULL,
    pump_room_id varchar(31) NOT NULL,
    scheme_name varchar(100) NOT NULL,
    scheme_code varchar(50) NOT NULL,
    scheme_description text,
    scheme_remark text,
    pump_group_config text NOT NULL,
    status smallint DEFAULT 1,
    creator varchar(31),
    create_time timestamp DEFAULT CURRENT_TIMESTAMP,
    updater varchar(31),
    update_time timestamp DEFAULT CURRENT_TIMESTAMP,
    tenant_id varchar(31),
    CONSTRAINT sp_pump_group_scheme_pkey PRIMARY KEY (id),
    CONSTRAINT uk_scheme_code UNIQUE (scheme_code)
);
```

## 后端接口

### 1. 控制器 (PumpGroupSchemeController)
- **路径**: `/api/sp/pumpGroupScheme`
- **注解**: `@IStarController2` (项目自定义注解)
- **代码风格**: 参考现有PumpHouseStorageController风格
- **功能**: 提供泵组方案的CRUD操作

#### 主要接口:
- `GET /api/sp/pumpGroupScheme` - 分页查询泵组方案
- `GET /api/sp/pumpGroupScheme/byPumpRoom/{pumpRoomId}` - 根据泵房ID获取方案列表
- `GET /api/sp/pumpGroupScheme/bySchemeCode/{schemeCode}` - 根据方案编码获取方案
- `POST /api/sp/pumpGroupScheme` - 保存泵组方案
- `POST /api/sp/pumpGroupScheme/batch` - 批量保存泵组方案
- `PATCH /api/sp/pumpGroupScheme/{id}` - 更新泵组方案
- `DELETE /api/sp/pumpGroupScheme/{id}` - 删除泵组方案

### 2. 服务层 (PumpGroupSchemeService)
- **实现类**: PumpGroupSchemeServiceImpl
- **功能**: 业务逻辑处理，包括数据验证和关联查询
- **代码风格**: 遵循项目现有服务层模式

### 3. 数据访问层 (PumpGroupSchemeMapper)
- **映射文件**: PumpGroupSchemeMapper.xml (PostgreSQL语法)
- **功能**: 数据库操作，支持复杂查询和关联查询
- **特点**: 使用CURRENT_TIMESTAMP替代NOW()函数

## 前端集成

### API文件
- **路径**: `water-guazhou-ui/src/api/waterMonitoring/pumpGroupScheme.ts`
- **功能**: 封装后端接口调用

### 页面更新
- **文件**: `water-guazhou-ui/src/views/waterMonitoring/pumpStation/index.vue`
- **更新内容**:
  - 导入泵组方案API
  - 修改数据获取逻辑，从获取泵机改为获取泵组方案
  - 更新表格列标题和数据映射

## 数据流程

1. **页面加载**: 获取泵房列表填充下拉选择
2. **选择泵房**: 调用 `getPumpGroupSchemeByPumpRoom(pumpRoomId)` 获取该泵房的方案列表
3. **数据展示**: 在表格中显示方案编码、方案名称、所属泵房、方案状态
4. **操作功能**: 支持添加、编辑、删除方案

## 示例数据

系统预置了示例数据：
- 龙车红星泵站标准方案 (LCHX_STD_001)
- 龙车红星泵站高峰方案 (LCHX_PEAK_001)  
- 龙车柑子院泵站标准方案 (LCGZY_STD_001)

## 泵组配置JSON格式

```json
[
  {
    "pumpName": "泵组1",
    "fixedPower": "15kW", 
    "fixedFlow": "50m³/h"
  },
  {
    "pumpName": "泵组2",
    "fixedPower": "18kW",
    "fixedFlow": "60m³/h"
  }
]
```

## 修正说明

### 1. PostgreSQL语法适配
- **数据类型**: 使用varchar(31)替代VARCHAR(36)
- **时间函数**: 使用CURRENT_TIMESTAMP替代NOW()
- **约束命名**: 使用PostgreSQL标准约束命名
- **索引创建**: 分离索引创建语句

### 2. 代码风格统一
- **控制器**: 移除多余注释，保持简洁风格
- **注解使用**: 使用项目标准@IStarController2注解
- **方法命名**: 遵循现有项目命名规范

## 部署说明

1. **执行SQL脚本**: 运行 `sql/sp_pump_group_scheme.sql` 创建表和示例数据
2. **重启服务**: 重启后端服务以加载新的接口
3. **前端更新**: 确保前端代码已更新并重新构建

## 测试验证

1. 访问泵站调度分析页面
2. 选择泵房，验证是否正确显示对应的泵组方案
3. 测试添加、编辑、删除功能
4. 验证数据持久化和状态更新
