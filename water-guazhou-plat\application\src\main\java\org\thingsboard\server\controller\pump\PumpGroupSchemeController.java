package org.thingsboard.server.controller.pump;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpGroupScheme;
import org.thingsboard.server.dao.pumpHouse.PumpGroupSchemeService;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpGroupSchemePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpGroupSchemeSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

import java.util.List;

@IStarController2
@RequestMapping("/api/sp/pumpGroupScheme")
public class PumpGroupSchemeController extends BaseController {
    @Autowired
    private PumpGroupSchemeService service;


    @GetMapping
    public IPage<PumpGroupScheme> findAllConditional(PumpGroupSchemePageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/byPumpRoom/{pumpRoomId}")
    public List<PumpGroupScheme> findByPumpRoomId(@PathVariable String pumpRoomId) {
        return service.findByPumpRoomId(pumpRoomId);
    }

    @GetMapping("/bySchemeCode/{schemeCode}")
    public PumpGroupScheme findBySchemeCode(@PathVariable String schemeCode) {
        return service.findBySchemeCode(schemeCode);
    }

    @GetMapping("/excel/template")
    public ExcelFileInfo excelTemplate() {
        return ExcelFileInfo.of("泵组方案模板")
                .withDate()
                .withoutTitle()
                .nextTitle("泵房ID")
                .nextTitle("方案名称")
                .nextTitle("方案编码")
                .nextTitle("方案描述")
                .nextTitle("方案备注")
                .nextTitle("泵组配置JSON")
                .nextTitle("状态（0-禁用，1-启用）");
    }

    @GetMapping("/excel/export")
    public ExcelFileInfo excelExport(PumpGroupSchemePageRequest request) {
        return ExcelFileInfo.of("泵组方案", findAllConditional(request.ignorePage()))
                .withDateTime()
                .nextTitle("pumpRoomCode", "泵房编码")
                .nextTitle("pumpRoomName", "泵房名称")
                .nextTitle("schemeName", "方案名称")
                .nextTitle("schemeCode", "方案编码")
                .nextTitle("schemeDescription", "方案描述")
                .nextTitle("schemeRemark", "方案备注")
                .nextTitle("status", "状态")
                .nextTitle("createTime", "创建时间");
    }

    @PostMapping
    public PumpGroupScheme save(@RequestBody PumpGroupSchemeSaveRequest req) {
        return service.save(req);
    }

    @PostMapping("/batch")
    public List<PumpGroupScheme> saveBatch(@RequestBody List<PumpGroupSchemeSaveRequest> req) {
        return service.saveAll(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody PumpGroupSchemeSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }

}
