<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.pumpHouse.PumpGroupSchemeMapper">
    
    <sql id="Base_Column_List">
        id,
        pump_room_id,
        (select code from sp_pump_house_storage where id = pump_room_id) pump_room_code,
        (select name from sp_pump_house_storage where id = pump_room_id) pump_room_name,
        scheme_name,
        scheme_code,
        scheme_description,
        scheme_remark,
        pump_group_config,
        status,
        creator,
        create_time,
        updater,
        update_time,
        tenant_id
    </sql>
    
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpGroupScheme">
        <result column="id" property="id"/>
        <result column="pump_room_id" property="pumpRoomId"/>
        <result column="pump_room_code" property="pumpRoomCode"/>
        <result column="pump_room_name" property="pumpRoomName"/>
        <result column="scheme_name" property="schemeName"/>
        <result column="scheme_code" property="schemeCode"/>
        <result column="scheme_description" property="schemeDescription"/>
        <result column="scheme_remark" property="schemeRemark"/>
        <result column="pump_group_config" property="pumpGroupConfig"/>
        <result column="status" property="status"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="updater" property="updater"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sp_pump_group_scheme
        <where>
            <if test="pumpRoomId != null and pumpRoomId != ''">
                AND pump_room_id = #{pumpRoomId}
            </if>
            <if test="pumpRoomName != null and pumpRoomName != ''">
                AND (select name from sp_pump_house_storage where id = pump_room_id) LIKE CONCAT('%', #{pumpRoomName}, '%')
            </if>
            <if test="pumpRoomCode != null and pumpRoomCode != ''">
                AND (select code from sp_pump_house_storage where id = pump_room_id) LIKE CONCAT('%', #{pumpRoomCode}, '%')
            </if>
            <if test="schemeName != null and schemeName != ''">
                AND scheme_name LIKE CONCAT('%', #{schemeName}, '%')
            </if>
            <if test="schemeCode != null and schemeCode != ''">
                AND scheme_code LIKE CONCAT('%', #{schemeCode}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="createTimeFrom != null">
                AND create_time >= #{createTimeFrom}
            </if>
            <if test="createTimeTo != null">
                AND create_time &lt;= #{createTimeTo}
            </if>
            <if test="tenantId != null and tenantId != ''">
                AND tenant_id = #{tenantId}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="findByPumpRoomId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sp_pump_group_scheme
        WHERE pump_room_id = #{pumpRoomId}
        AND status = 1
        ORDER BY create_time DESC
    </select>

    <select id="findBySchemeCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sp_pump_group_scheme
        WHERE scheme_code = #{schemeCode}
        LIMIT 1
    </select>

    <update id="update">
        UPDATE sp_pump_group_scheme
        <set>
            <if test="pumpRoomId != null and pumpRoomId != ''">pump_room_id = #{pumpRoomId},</if>
            <if test="schemeName != null and schemeName != ''">scheme_name = #{schemeName},</if>
            <if test="schemeCode != null and schemeCode != ''">scheme_code = #{schemeCode},</if>
            <if test="schemeDescription != null">scheme_description = #{schemeDescription},</if>
            <if test="schemeRemark != null">scheme_remark = #{schemeRemark},</if>
            <if test="pumpGroupConfig != null">pump_group_config = #{pumpGroupConfig},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            update_time = CURRENT_TIMESTAMP
        </set>
        WHERE id = #{id}
    </update>

    <insert id="saveAll">
        INSERT INTO sp_pump_group_scheme(id,
                                        pump_room_id,
                                        scheme_name,
                                        scheme_code,
                                        scheme_description,
                                        scheme_remark,
                                        pump_group_config,
                                        status,
                                        creator,
                                        create_time,
                                        tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.pumpRoomId},
             #{element.schemeName},
             #{element.schemeCode},
             #{element.schemeDescription},
             #{element.schemeRemark},
             #{element.pumpGroupConfig},
             #{element.status},
             #{element.creator},
             #{element.createTime},
             #{element.tenantId})
        </foreach>
    </insert>

    <update id="updateAll">
        UPDATE sp_pump_group_scheme
        SET pump_room_id = valueTable.pump_room_id,
            scheme_name = valueTable.scheme_name,
            scheme_code = valueTable.scheme_code,
            scheme_description = valueTable.scheme_description,
            scheme_remark = valueTable.scheme_remark,
            pump_group_config = valueTable.pump_group_config,
            status = valueTable.status,
            updater = valueTable.updater,
            update_time = CURRENT_TIMESTAMP
        FROM (VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.pumpRoomId},
             #{element.schemeName},
             #{element.schemeCode},
             #{element.schemeDescription},
             #{element.schemeRemark},
             #{element.pumpGroupConfig},
             #{element.status},
             #{element.updater})
        </foreach>
        ) as valueTable(id, pump_room_id, scheme_name, scheme_code, scheme_description, scheme_remark, pump_group_config, status, updater)
        WHERE sp_pump_group_scheme.id = valueTable.id
    </update>

</mapper>
