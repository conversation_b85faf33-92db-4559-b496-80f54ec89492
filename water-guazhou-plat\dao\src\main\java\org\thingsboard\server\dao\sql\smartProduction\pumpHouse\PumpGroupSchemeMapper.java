package org.thingsboard.server.dao.sql.smartProduction.pumpHouse;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpGroupScheme;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpGroupSchemePageRequest;

import java.util.List;

@Mapper
public interface PumpGroupSchemeMapper extends BaseMapper<PumpGroupScheme> {
    
    /**
     * 分页查询泵组方案
     */
    IPage<PumpGroupScheme> findByPage(PumpGroupSchemePageRequest request);

    /**
     * 更新泵组方案
     */
    boolean update(PumpGroupScheme entity);

    /**
     * 批量保存泵组方案
     */
    int saveAll(List<PumpGroupScheme> list);

    /**
     * 批量更新泵组方案
     */
    int updateAll(List<PumpGroupScheme> list);

    /**
     * 根据泵房ID获取方案列表
     */
    List<PumpGroupScheme> findByPumpRoomId(String pumpRoomId);

    /**
     * 根据方案编码获取方案信息
     */
    PumpGroupScheme findBySchemeCode(String schemeCode);

}
