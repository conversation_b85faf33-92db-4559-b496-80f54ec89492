# 添加泵组方案功能更新说明

## 概述
更新了添加泵组方案功能，现在选择泵机时会从 `api/sp/pumpManage` 接口查询真实的泵机数据，并且查询参数会带上当前选择的泵房ID。

## 主要更新

### 1. AddSchemeDialog 组件更新

#### 1.1 新增Props
```typescript
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  pumpRoomId: {  // 新增：泵房ID
    type: String,
    default: ''
  }
})
```

#### 1.2 泵机数据查询
```typescript
// 获取泵机列表
const getPumpList = async () => {
  if (!props.pumpRoomId) {
    pumpOptions.value = []
    return
  }

  try {
    const res = await pumpManageList({
      page: 1,
      size: 1000,
      pumpRoomId: props.pumpRoomId  // 使用传入的泵房ID
    })

    // 解析数据并转换为下拉选项
    pumpOptions.value = pumps.map((item: any) => ({
      label: `${item.name} (${item.code})`, // 显示设备名称和编码
      value: item.id,
      data: item // 保存完整的泵机数据
    }))
  } catch (error) {
    console.error('获取泵机列表失败:', error)
    ElMessage.error('获取泵机列表失败')
  }
}
```

#### 1.3 泵机选择处理
```typescript
// 处理泵机选择变化
const handlePumpChange = (row: any, pumpId: string) => {
  if (pumpId) {
    const selectedPump = pumpOptions.value.find(option => option.value === pumpId)
    if (selectedPump && selectedPump.data) {
      // 自动填充功率和流量信息（如果有性能参数）
      if (selectedPump.data.performanceParameters) {
        try {
          const params = JSON.parse(selectedPump.data.performanceParameters)
          row.fixedPower = params.power || ''
          row.fixedFlow = params.flow || ''
        } catch (error) {
          console.log('解析性能参数失败:', error)
        }
      }
      row.pumpDisplayName = selectedPump.label
    }
  } else {
    // 清空相关字段
    row.fixedPower = ''
    row.fixedFlow = ''
    row.pumpDisplayName = ''
  }
}
```

#### 1.4 模板更新
```vue
<el-table-column prop="pumpName" label="启用水泵" width="200">
  <template #default="{ row }">
    <el-select v-model="row.pumpName" placeholder="请选择泵机" @change="handlePumpChange(row, $event)">
      <el-option label="请选择" value="" />
      <el-option 
        v-for="option in pumpOptions" 
        :key="option.value" 
        :label="option.label" 
        :value="option.value" 
      />
    </el-select>
  </template>
</el-table-column>
```

#### 1.5 监听器更新
```typescript
// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
    getPumpList() // 对话框打开时获取泵机列表
  }
})

// 监听泵房ID变化
watch(() => props.pumpRoomId, (newVal) => {
  if (newVal && props.visible) {
    getPumpList() // 泵房ID变化时重新获取泵机列表
  }
})
```

### 2. 主页面更新

#### 2.1 组件调用更新
```vue
<!-- 添加方案对话框 -->
<AddSchemeDialog
  v-model:visible="addSchemeDialogVisible"
  :pump-room-id="queryForm.stationId"  <!-- 传入当前选择的泵房ID -->
  @confirm="handleAddScheme"
/>
```

#### 2.2 添加验证
```typescript
// 显示添加方案对话框
const showAddSchemeDialog = () => {
  if (!queryForm.stationId) {
    ElMessage.warning('请先选择泵房')
    return
  }
  addSchemeDialogVisible.value = true
}
```

## 数据流程

1. **用户选择泵房**: 在主页面下拉框中选择泵房
2. **点击添加按钮**: 验证是否已选择泵房
3. **打开对话框**: 传入泵房ID，自动调用泵机查询接口
4. **选择泵机**: 下拉框显示该泵房下的所有泵机
5. **自动填充**: 选择泵机后自动填充功率和流量信息（如果有）
6. **提交方案**: 保存包含真实泵机ID的方案配置

## API调用

### 泵机查询接口
- **接口**: `GET /api/sp/pumpManage`
- **参数**: 
  ```typescript
  {
    page: 1,
    size: 1000,
    pumpRoomId: string  // 当前选择的泵房ID
  }
  ```
- **返回**: 该泵房下的所有泵机列表

### 数据结构
```typescript
// 泵机选项格式
{
  label: "设备名称 (设备编码)",  // 显示文本
  value: "泵机ID",              // 选择值
  data: {                      // 完整泵机数据
    id: string,
    code: string,
    name: string,
    performanceParameters: string  // JSON格式的性能参数
  }
}

// 泵组配置格式
{
  pumpName: string,           // 选择的泵机ID
  fixedPower: string,         // 额定功率
  fixedFlow: string,          // 额定流量
  pumpDisplayName: string     // 显示名称
}
```

## 特性

1. **动态加载**: 根据选择的泵房动态加载对应的泵机列表
2. **智能填充**: 选择泵机后自动填充性能参数（如果有）
3. **数据验证**: 确保选择泵房后才能添加方案
4. **用户友好**: 显示泵机名称和编码，便于识别
5. **错误处理**: 完善的错误提示和容错机制

现在添加方案功能完全基于真实的泵机数据，用户可以从实际的泵机列表中选择，确保方案配置的准确性！
