<template>
  <el-dialog
    v-model="dialogVisible"
    title="泵组方案"
    width="800px"
    :before-close="handleClose"
  >
    <div class="scheme-dialog">
      <el-form :model="form" label-width="100px">
        <el-form-item label="方案名称:">
          <el-input v-model="form.schemeName" placeholder="请输入..." style="width: 300px;" />
        </el-form-item>
      </el-form>

      <div class="pump-config-section">
        <div class="section-title">泵组配置</div>
        <el-table :data="pumpConfigs" border style="width: 100%;">
          <el-table-column prop="pumpName" label="启用水泵" width="150">
            <template #default="{ row, $index }">
              <el-select v-model="row.pumpName" placeholder="请选择">
                <el-option label="请选择" value="" />
                <el-option label="泵组1" value="泵组1" />
                <el-option label="泵组2" value="泵组2" />
                <el-option label="泵组3" value="泵组3" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="fixedPower" label="额定功率" width="150">
            <template #default="{ row, $index }">
              <el-input v-model="row.fixedPower" placeholder="请输入..." />
            </template>
          </el-table-column>
          <el-table-column prop="fixedFlow" label="额定流量" width="150">
            <template #default="{ row, $index }">
              <el-input v-model="row.fixedFlow" placeholder="请输入..." />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ row, $index }">
              <el-button type="primary" link size="small" @click="deletePumpConfig($index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="add-pump-btn">
          <el-button type="primary" @click="addPumpConfig">添加水泵</el-button>
        </div>
      </div>

      <div class="scheme-description">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="description-item">
              <label>方案描述:</label>
              <el-input
                v-model="form.schemeDescription"
                type="textarea"
                :rows="3"
                placeholder="通过实时水泵运行计算组团"
              />
            </div>
          </el-col>
          <el-col :span="12">
            <div class="description-item">
              <label>方案备注:</label>
              <el-input
                v-model="form.schemeRemark"
                type="textarea"
                :rows="3"
                placeholder="通过实时水泵运行计算组团"
              />
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">提交</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'confirm'])

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单数据
const form = reactive({
  schemeName: '',
  schemeDescription: '',
  schemeRemark: ''
})

// 泵组配置数据
const pumpConfigs = ref([
  {
    pumpName: '',
    fixedPower: '',
    fixedFlow: ''
  },
  {
    pumpName: '',
    fixedPower: '',
    fixedFlow: ''
  }
])

// 添加泵组配置
const addPumpConfig = () => {
  pumpConfigs.value.push({
    pumpName: '',
    fixedPower: '',
    fixedFlow: ''
  })
}

// 删除泵组配置
const deletePumpConfig = (index: number) => {
  if (pumpConfigs.value.length > 1) {
    pumpConfigs.value.splice(index, 1)
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 确认提交
const handleConfirm = () => {
  const schemeData = {
    ...form,
    pumpConfigs: pumpConfigs.value
  }
  emit('confirm', schemeData)
  resetForm()
}

// 重置表单
const resetForm = () => {
  form.schemeName = ''
  form.schemeDescription = ''
  form.schemeRemark = ''
  pumpConfigs.value = [
    {
      pumpName: '',
      fixedPower: '',
      fixedFlow: ''
    },
    {
      pumpName: '',
      fixedPower: '',
      fixedFlow: ''
    }
  ]
}

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
  }
})
</script>

<style scoped lang="scss">
.scheme-dialog {
  .pump-config-section {
    margin: 20px 0;
    
    .section-title {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 10px;
    }
    
    .add-pump-btn {
      margin-top: 10px;
      text-align: center;
    }
  }
  
  .scheme-description {
    margin-top: 20px;
    
    .description-item {
      label {
        display: block;
        margin-bottom: 5px;
        font-size: 14px;
      }
    }
  }
}

.dialog-footer {
  text-align: center;
}
</style>
