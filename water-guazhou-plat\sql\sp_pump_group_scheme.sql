-- 泵组方案表
CREATE TABLE IF NOT EXISTS sp_pump_group_scheme (
    id VARCHAR(36) NOT NULL COMMENT '主键ID',
    pump_room_id VARCHAR(36) NOT NULL COMMENT '所属泵房ID',
    scheme_name VARCHAR(100) NOT NULL COMMENT '方案名称',
    scheme_code VARCHAR(50) NOT NULL COMMENT '方案编码',
    scheme_description TEXT COMMENT '方案描述',
    scheme_remark TEXT COMMENT '方案备注',
    pump_group_config TEXT NOT NULL COMMENT '泵组配置JSON',
    status TINYINT(1) DEFAULT 1 COMMENT '状态(0-禁用,1-启用)',
    creator VARCHA<PERSON>(36) COMMENT '创建人',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(36) COMMENT '更新人',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    tenant_id VARCHAR(36) COMMENT '租户ID',
    PRIMARY KEY (id),
    UNIQUE KEY uk_scheme_code (scheme_code),
    KEY idx_pump_room_id (pump_room_id),
    KEY idx_scheme_name (scheme_name),
    KEY idx_status (status),
    KEY idx_create_time (create_time),
    KEY idx_tenant_id (tenant_id),
    CONSTRAINT fk_pump_group_scheme_pump_room FOREIGN KEY (pump_room_id) REFERENCES sp_pump_house_storage(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='泵组方案表';

-- 插入示例数据
INSERT INTO sp_pump_group_scheme (
    id, 
    pump_room_id, 
    scheme_name, 
    scheme_code, 
    scheme_description, 
    scheme_remark, 
    pump_group_config, 
    status, 
    creator, 
    tenant_id
) VALUES 
(
    'scheme_001', 
    'f8692bc87927871a717cd0e46d2e8b16', 
    '龙车红星泵站标准方案', 
    'LCHX_STD_001', 
    '龙车红星泵站的标准运行方案，适用于正常供水需求', 
    '该方案为默认推荐方案', 
    '[{"pumpName":"泵组1","fixedPower":"15kW","fixedFlow":"50m³/h"},{"pumpName":"泵组2","fixedPower":"18kW","fixedFlow":"60m³/h"}]', 
    1, 
    '1ed79f7c4ea3600ac4b4d3910c94b0c', 
    '1ed79ece3d7b2009c4b6f2427406ab1'
),
(
    'scheme_002', 
    'f8692bc87927871a717cd0e46d2e8b16', 
    '龙车红星泵站高峰方案', 
    'LCHX_PEAK_001', 
    '龙车红星泵站的高峰期运行方案，适用于用水高峰时段', 
    '高峰期启用更多泵组', 
    '[{"pumpName":"泵组1","fixedPower":"15kW","fixedFlow":"50m³/h"},{"pumpName":"泵组2","fixedPower":"18kW","fixedFlow":"60m³/h"},{"pumpName":"泵组3","fixedPower":"20kW","fixedFlow":"70m³/h"}]', 
    1, 
    '1ed79f7c4ea3600ac4b4d3910c94b0c', 
    '1ed79ece3d7b2009c4b6f2427406ab1'
),
(
    'scheme_003', 
    '7ec5dc1dab74d1534f6f4ef93b4cd4ff', 
    '龙车柑子院泵站标准方案', 
    'LCGZY_STD_001', 
    '龙车柑子院泵站的标准运行方案', 
    '适用于日常供水', 
    '[{"pumpName":"泵组1","fixedPower":"12kW","fixedFlow":"45m³/h"},{"pumpName":"泵组2","fixedPower":"15kW","fixedFlow":"55m³/h"}]', 
    1, 
    '1ed79f7c4ea3600ac4b4d3910c94b0c', 
    '1ed79ece3d7b2009c4b6f2427406ab1'
);
