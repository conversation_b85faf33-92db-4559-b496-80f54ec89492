# 泵站调度分析页面重新设计

## 概述
根据原型设计要求，重新设计了泵站调度分析页面，实现了以下功能：

## 主要功能

### 1. 筛选面板
- **数据来源选择**: 下拉选择泵站
- **方案名称输入**: 文本输入框
- **查询/重置按钮**: 执行查询和重置操作

### 2. 左侧泵房列表
- **表格显示**: 显示泵机编号、泵机名称、所属泵房、泵机状态
- **选择功能**: 支持多选泵机
- **状态标识**: 运行状态用绿色标签，停机状态用灰色标签
- **操作按钮**: 编辑和删除功能
- **添加按钮**: 右上角添加方案按钮

### 3. 右侧图表区域

#### 3.1 供水量对比图表（上方）
- **图表类型**: 柱状图
- **数据对比**: 中水泵 vs 供水泵
- **图例显示**: 蓝色代表中水泵，绿色代表供水泵
- **数据维度**: 按泵组显示（泵组1-5

#### 3.2 泵组耗水耗电图表（下方）
- **图表类型**: 柱状图
- **数据内容**: 各泵组的耗电量
- **单位显示**: kWh/t
- **颜色标识**: 黄色柱状图

### 4. 添加方案对话框
- **方案名称**: 文本输入
- **泵组配置表格**: 
  - 启用水泵下拉选择
  - 额定功率输入
  - 额定流量输入
  - 删除操作
- **添加水泵按钮**: 动态添加泵组配置
- **方案描述**: 多行文本输入
- **方案备注**: 多行文本输入

## 技术实现

### 1. 组件结构
```
index.vue (主页面)
├── AddSchemeDialog.vue (添加方案对话框)
```

### 2. 主要技术栈
- **Vue 3 Composition API**: 响应式数据管理
- **Element Plus**: UI组件库
- **ECharts**: 图表渲染
- **TypeScript**: 类型安全

### 3. 数据流程
1. 页面加载时获取泵站列表
2. 选择数据来源后获取对应泵站数据
3. 动态渲染表格和图表
4. 支持添加、编辑、删除操作

### 4. 图表配置
- **响应式设计**: 图表自适应容器大小
- **交互功能**: 支持tooltip显示详细数据
- **样式统一**: 遵循设计规范的颜色和样式

## 数据接口
### 已接入的真实API
1. **泵房列表**: 使用 `pumpHouseStorageList` 接口获取泵房数据
2. **泵机数据**: 使用 `pumpManageList` 接口根据泵房ID获取对应的泵机信息

### 接口说明
- **数据来源下拉**: 调用泵房台账接口，显示所有可用泵房
- **泵机列表**: 根据选中的泵房ID查询该泵房下的所有泵机
- **容错处理**: 当接口调用失败时，会显示模拟数据作为备选方案

## 布局优化
### 调整前问题
- 左侧表格列宽度固定，内容显示不完整
- 表格区域过窄，数据挤压严重

### 调整后改进
- **列比例调整**: 左侧表格区域从 10/24 调整为 14/24，右侧图表区域从 14/24 调整为 10/24
- **列宽优化**: 使用 `min-width` 替代固定 `width`，让列宽自适应内容
- **图表高度**: 调整图表高度从 200px 到 180px，更好适应新布局

## 后续优化
1. 添加数据刷新机制
2. 完善错误处理和用户提示
3. 添加数据导出功能
4. 优化图表交互体验
5. 添加实时数据更新

## 使用说明
1. 选择数据来源（泵站）
2. 查看左侧泵房列表
3. 观察右侧图表数据
4. 点击"添加"按钮配置新方案
5. 使用编辑/删除功能管理泵机
