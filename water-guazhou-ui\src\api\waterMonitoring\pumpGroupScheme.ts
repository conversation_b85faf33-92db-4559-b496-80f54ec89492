import request from '@/utils/request'

// 泵组方案相关接口

/**
 * 分页查询泵组方案
 */
export function getPumpGroupSchemeList(params: any) {
  return request({
    url: '/api/sp/pumpGroupScheme',
    method: 'get',
    params
  })
}

/**
 * 根据泵房ID获取方案列表
 */
export function getPumpGroupSchemeByPumpRoom(pumpRoomId: string) {
  return request({
    url: `/api/sp/pumpGroupScheme/byPumpRoom/${pumpRoomId}`,
    method: 'get'
  })
}

/**
 * 根据方案编码获取方案信息
 */
export function getPumpGroupSchemeByCode(schemeCode: string) {
  return request({
    url: `/api/sp/pumpGroupScheme/bySchemeCode/${schemeCode}`,
    method: 'get'
  })
}

/**
 * 保存泵组方案
 */
export function savePumpGroupScheme(data: any) {
  return request({
    url: '/api/sp/pumpGroupScheme',
    method: 'post',
    data
  })
}

/**
 * 批量保存泵组方案
 */
export function savePumpGroupSchemeBatch(data: any[]) {
  return request({
    url: '/api/sp/pumpGroupScheme/batch',
    method: 'post',
    data
  })
}

/**
 * 更新泵组方案
 */
export function updatePumpGroupScheme(id: string, data: any) {
  return request({
    url: `/api/sp/pumpGroupScheme/${id}`,
    method: 'patch',
    data
  })
}

/**
 * 删除泵组方案
 */
export function deletePumpGroupScheme(id: string) {
  return request({
    url: `/api/sp/pumpGroupScheme/${id}`,
    method: 'delete'
  })
}

/**
 * 导出Excel模板
 */
export function exportPumpGroupSchemeTemplate() {
  return request({
    url: '/api/sp/pumpGroupScheme/excel/template',
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 导出Excel数据
 */
export function exportPumpGroupSchemeData(params: any) {
  return request({
    url: '/api/sp/pumpGroupScheme/excel/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
