package org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpGroupScheme;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class PumpGroupSchemePageRequest extends AdvancedPageableQueryEntity<PumpGroupScheme, PumpGroupSchemePageRequest> {
    
    // 所属泵房ID
    private String pumpRoomId;

    // 所属泵房名称
    private String pumpRoomName;

    // 所属泵房编码
    private String pumpRoomCode;

    // 方案名称
    private String schemeName;

    // 方案编码
    private String schemeCode;

    // 状态 (0-禁用, 1-启用)
    private Integer status;

    // 创建时间From
    private String createTimeFrom;

    // 创建时间To
    private String createTimeTo;

    public Date getCreateTimeFrom() {
        return toDate(createTimeFrom);
    }

    public Date getCreateTimeTo() {
        return toDate(createTimeTo);
    }

}
